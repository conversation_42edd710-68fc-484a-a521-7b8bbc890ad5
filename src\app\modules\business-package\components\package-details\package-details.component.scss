.package-details {
  text-align: right;
  direction: rtl;
  color: #272728;
  font-size: 14px;
}

.item {
  margin-bottom: 8px;
}

.label {
  font-weight: 700;
}

.value {
  font-weight: 400;
  display: inline;
}
li {
    list-style: none;
}
.dot-icon {
  display: inline-block;
  width: 6px;
  height: 6px;
  background-color: #272728;
  margin-left: 6px;
}
.renewal-note {
  width: 287px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 6px;
  margin-top: 12px;

  .note-text {
    font-size: 10px;
    font-weight: 700;
    color: #7C7C7C;
    line-height: 28px;
    text-align: right;
  }

  .note-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }
}
/*****************************************************************************/
.plan-card {
  width: 327px;
  padding: 20px;
  margin: 20px 0px;
  background: #EFEFEF; 
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  direction: rtl;
  text-align: right;
}

.badge-wrapper {
  display: flex;
  justify-content: flex-start;
}

.badge {
  background: #FAAF40;
  border-radius: 3px;
  padding: 2px 12px;
  color: white; 
  font-size: 12px;
  font-weight: 600;
}

.plan-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.price-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.price {
  color: #722282; 
  font-weight: 800;
  font-size: 24px;
  line-height: 28px;
}

.price-unit {
  font-size: 12px;
  font-weight: 600;
  margin-right: 4px;
}

.plan-title {
  color: #722282;
  font-size: 14px;
  font-weight: 700;
}
/*****************************************************************************/
.sales-btn {
  width: 287px;
  height: 38px;
  padding: 6px 12px;
  background: white;
  box-shadow: inset 0px -3px 6px rgba(243, 245, 250, 0.6);
  border-radius: 6px;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  transition: #722282 0.2s ease, box-shadow 0.2s ease;
  direction: ltr;
}

.sales-btn:hover {
  background: #f8f8f8;
  box-shadow: inset 0px -3px 8px rgba(243, 245, 250, 0.8);
}

.icon-wrapper svg path {
  fill: #722282;
}

.sales-btn-label {
  text-align: center;
  color: #722282;
  font-size: 14px;
  font-weight: 700;
  word-wrap: break-word;
}
/*****************************************************************************/
.plans-modal {
  width: 100%;
  max-width: 375px;
  background: #ffffff;
  box-shadow: 0px 2px 20px rgba(0,0,0,0.25);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  direction: rtl;
}

/* Header */
.plans-header {
  background: radial-gradient(ellipse 66% 66% at 50% 38%, rgba(255,205,132,0.2) 0%, #FAAF40 87%);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}
.header-content {
  padding: 0 24px 38px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
.support-btn {
  display: flex;
  align-items: center;
  gap: 4px;
}
.text-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  color: #722282;
  font-weight: 700;
  font-size: 14px;
  cursor: pointer;
}
.hero-image {
  display: flex;
  justify-content: center;
}

/* Plans */
.plans-body {
  padding: 24px;
}

/*****************************************************************************/

.cancel-package-modal {
  width: 375px;
  background: white;
  box-shadow: 0px 2px 20px rgba(0, 0, 0, 0.25);
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  padding: 16px 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  font-family: "Cairo", sans-serif;
}

.modal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.drag-bar {
  width: 33px;
  height: 4px;  
}

.modal-title {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  gap: 4px;
  width: 100%;
}

.title-icon {
  flex-shrink: 0;
}

.title-text {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  text-align: right;
  font-size: 16px;
  font-weight: 800;
  color: #272728;
  line-height: 28px;
}

.cancel-date {
  width: 100%;
  text-align: right;
  font-size: 14px;
  color: #7C7C7C;
  line-height: 1.6;
}

.cancel-date .date {
  font-weight: 700;
}


.cancel-btn {
  width: 325px;
  height: 48px;
  background: #B1362F;
  box-shadow: 0px 0px 0px 1px #9A3A2D;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
}

.bottom-bar {
  width: 117px;
  height: 3.49px;
}
/*****************************************************************************/
.ads-range{
  width: 360px;
  color:#272728;
}

.ads-range__header{
  font-weight:800;
  font-size:16px;
  line-height:1;
  margin-bottom:6px;
}

.ads-range__row{
  display:flex;
  align-items:center;
  gap:10px;
}

.ads-range__value,
.ads-range__min{
  font-size:12px;
  font-weight:700;
  color:#7C7C7C;
  text-align:start;  
}

.ads-range__track{
  flex:1;
  display:flex;
  align-items:center;
}

#adsRange{
  appearance:none;
  width:100%;
  height:6px;
  background: linear-gradient(to left, #FAAF40 calc(var(--val, .5)*100%), #D9D9D9 0);
  border-radius:999px;
  outline:none;
  cursor:pointer;
}

#adsRange::-webkit-slider-thumb{
  appearance:none;
  width:18px;
  height:18px;
  border-radius:50%;
  background: #FAAF40;
  margin-top:-6px;              
}

#adsRange::-moz-range-track{
  height:6px;
  background: transparent;       
}
#adsRange::-moz-range-progress{
  height:6px;
  background: #FAAF40;
  border-radius:999px 0 0 999px;
}
#adsRange::-moz-range-thumb{
  width:18px;
  height:18px;
  border-radius:50%;
  background:#FAAF40;
  border: 2px solid #fff;
  box-shadow: 0 0 0 2px rgba(0,0,0,.06);
}

#adsRange:focus-visible{
  outline: none;
  box-shadow: 0 0 0 3px rgba(250,175,64,.25);
  border-radius: 999px;
}
