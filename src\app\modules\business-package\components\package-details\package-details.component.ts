import { <PERSON><PERSON><PERSON> } from "@angular/common";
import { Component, Input } from "@angular/core";
import { SharedBtnComponent } from "@src/app/shared/components/shared-btn/shared-btn.component";

@Component({
  selector: "app-package-details",
  standalone: true,
  imports: [NgF<PERSON>, SharedBtnComponent],
  templateUrl: "./package-details.component.html",
  styleUrl: "./package-details.component.scss",
})
export class PackageDetailsComponent {
  @Input() duration!: string;
  @Input() adCount!: number;
  @Input() availablePoints!: number;
  @Input() features: string[] = [];
  @Input() support!: string;
  @Input() renewalNote: string = "";

}
