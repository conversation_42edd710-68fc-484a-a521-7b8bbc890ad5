import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-new-range-input',
  standalone: true,
  imports: [],
  templateUrl: './new-range-input.component.html',
  styleUrl: './new-range-input.component.scss'
})
export class NewRangeInputComponent {
  @Input() min = 0;
  @Input() max = 10;
  @Input() step = 1;
  @Input() value = 5;
  @Input() header = 'حدد عدد الإعلانات';

  progressPercent = 0;

  ngOnInit() {
    this.updateProgress();
  }

  onRangeChange(event: Event) {
    const input = event.target as HTMLInputElement;
    this.value = Number(input.value);
    this.updateProgress();
  }

  private updateProgress() {
    this.progressPercent = ((this.value - this.min) / (this.max - this.min)) * 100;
  }
}
